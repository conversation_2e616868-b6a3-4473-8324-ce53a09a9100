from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone

from gee.services.analysis_orchestrator import AnalysisOrchestrator
from api.serializers.image_serializer import ImageSerializer
from image.models.image_model import ImageModel
from permissions.CanLauchAnalysis import CanLaunchAnalysis

class AnalysisViewSet(viewsets.GenericViewSet):
    permission_classes = [CanLaunchAnalysis]
    """
    ViewSet pour les analyses d'orpaillage
    - Seul endpoint : POST /api/analysis/run/
    """
    @action(detail=False, methods=['post'], url_path='run')
    def run_analysis(self, request):
        """
        Lance une analyse complète de détection d'orpaillage
        POST /api/analysis/run/
        Body: {"months_back": 3}  # Optionnel, défaut: 3 mois
        """
        try:
            print("DEBUG: Début analyse")
            # Paramètres
            months_back = request.data.get('months_back', 3)
            user_id = None #request.user.id if request.user.is_authenticated else None

            # Validation
            if not isinstance(months_back, int) or months_back < 1 or months_back > 12:
                return Response({
                    'error': 'months_back doit être un entier entre 1 et 12'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Lancement analyse
            print("DEBUG: Création orchestrateur")
            orchestrator = AnalysisOrchestrator()
            print("DEBUG: Orchestrateur créé")

            results = orchestrator.run_complete_analysis(months_back, user_id)
            print(f"DEBUG: Résultats = {results}")

            if results['success']:
                return Response({
                    'success': True,
                    'message': 'Analyse terminée avec succès',
                    'data': {
                        'images_processed': results['images_processed'],
                        'detections_found': results['detections_found'],
                        'alerts_generated': results['alerts_generated'],
                        'analysis_date': timezone.now().isoformat()
                    }
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': 'Erreurs lors de l\'analyse',
                    'errors': results['errors']
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'error': f'Erreur inattendue: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)