from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny  # Temporaire
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.utils import timezone

from detection.models.investigation_model import InvestigationModel
from detection.models.detection_feedback_model import DetectionFeedbackModel
from api.serializers.investigation_serializer import InvestigationSerializer

from permissions.CanManageInvestigations import CanManageInvestigations
class InvestigationViewSet(viewsets.ModelViewSet):
    permission_classes = [CanManageInvestigations]
    """
    ViewSet pour les investigations terrain
    - GET /api/v1/investigations/ - Liste investigations
    - GET /api/v1/investigations/pending/ - Investigations en attente
    - PATCH /api/v1/investigations/{id}/assign/ - Assigner agent
    - PATCH /api/v1/investigations/{id}/result/ - Résultat terrain
    """
    queryset = InvestigationModel.objects.all()
    serializer_class = InvestigationSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'result', 'assigned_to']
    ordering_fields = ['created_at', 'investigation_date']
    ordering = ['-created_at']

    # Blocage création manuelle - généré automatiquement
    http_method_names = ['get', 'put', 'patch', 'head', 'options']

    @action(detail=False, methods=['get'], url_path='pending')
    def pending_investigations(self, request):
        """
        Investigations en attente d'assignation
        GET /api/v1/investigations/pending/
        """
        pending = self.queryset.filter(status='PENDING').order_by('-created_at')
        serializer = self.get_serializer(pending, many=True)
        return Response({
            'count': pending.count(),
            'results': serializer.data
        })

    @action(detail=False, methods=['get'], url_path='assigned-to-me')
    def my_investigations(self, request):
        """
        Investigations assignées à l'utilisateur connecté
        GET /api/v1/investigations/assigned-to-me/
        """
        if not request.user.is_authenticated:
            return Response({'error': 'Authentification requise'},
                            status=status.HTTP_401_UNAUTHORIZED)

        my_investigations = self.queryset.filter(
            assigned_to=request.user,
            status__in=['ASSIGNED', 'IN_PROGRESS']
        ).order_by('-created_at')

        serializer = self.get_serializer(my_investigations, many=True)
        return Response({
            'count': my_investigations.count(),
            'results': serializer.data
        })

    @action(detail=True, methods=['patch'], url_path='assign')
    def assign_investigation(self, request, pk=None):
        """
        Assigner une investigation à un agent terrain
        PATCH /api/v1/investigations/{id}/assign/
        Body: {"assigned_to": user_id}
        """
        try:
            investigation = self.get_object()
            assigned_to_id = request.data.get('assigned_to')

            if not assigned_to_id:
                return Response({
                    'error': 'assigned_to est requis'
                }, status=status.HTTP_400_BAD_REQUEST)

            investigation.assigned_to_id = assigned_to_id
            investigation.status = 'ASSIGNED'
            investigation.save()

            serializer = self.get_serializer(investigation)
            return Response({
                'message': 'Investigation assignée avec succès',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur assignation: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['patch'], url_path='result')
    def submit_result(self, request, pk=None):
        """
        Soumettre résultat d'investigation terrain
        PATCH /api/v1/investigations/{id}/result/
        Body: {
            "result": "CONFIRMED",
            "field_notes": "Site confirmé, activité intense",
            "investigation_date": "2025-05-30"
        }
        """
        try:
            investigation = self.get_object()

            # Mise à jour investigation
            result = request.data.get('result')
            field_notes = request.data.get('field_notes', '')
            investigation_date = request.data.get('investigation_date')

            if result not in ['CONFIRMED', 'FALSE_POSITIVE', 'NEEDS_MONITORING']:
                return Response({
                    'error': 'result doit être CONFIRMED, FALSE_POSITIVE ou NEEDS_MONITORING'
                }, status=status.HTTP_400_BAD_REQUEST)

            investigation.result = result
            investigation.field_notes = field_notes
            investigation.status = 'COMPLETED'

            if investigation_date:
                from datetime import datetime
                investigation.investigation_date = datetime.strptime(investigation_date, '%Y-%m-%d').date()
            else:
                investigation.investigation_date = timezone.now().date()

            investigation.save()

            # Création automatique du feedback pour apprentissage
            self._create_detection_feedback(investigation)

            serializer = self.get_serializer(investigation)
            return Response({
                'message': 'Résultat investigation enregistré',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'error': f'Erreur soumission résultat: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_detection_feedback(self, investigation):
        """Créer feedback pour amélioration future de l'IA"""
        try:
            detection = investigation.detection

            # Création feedback avec scores originaux
            feedback = DetectionFeedbackModel.objects.create(
                detection=detection,
                investigation=investigation,
                original_confidence=detection.confidence_score,
                original_ndvi_score=detection.ndvi_anomaly_score or 0,
                original_ndwi_score=detection.ndwi_anomaly_score or 0,
                original_ndti_score=detection.ndti_anomaly_score or 0,
                ground_truth_confirmed=(investigation.result == 'CONFIRMED'),
                agent_confidence=2,  # Valeur par défaut, peut être paramétré
                used_for_training=False
            )

            print(f"Feedback créé pour détection {detection.id}")

        except Exception as e:
            print(f"Erreur création feedback: {e}")