#!/usr/bin/env python
"""
Script pour créer un utilisateur Responsable Régional dans GoldSentinel
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'gold_sentinel.settings')
django.setup()

from account.models.user_model import UserModel
from account.models.authority_model import AuthorityModel
from account.models.user_authority_model import UserAuthorityModel
from region.models.region_model import RegionModel
from django.contrib.auth.hashers import make_password

def create_responsable_regional():
    print("=== CRÉATION RESPONSABLE RÉGIONAL GOLDSENTINEL ===")
    print()
    
    # 1. Créer les autorités si elles n'existent pas
    print("1. Création des autorités...")
    authorities_data = [
        ('Administrateur', 'Administration complète du système'),
        ('Responsable Régional', 'Supervision complète surveillance orpaillage'),
        ('Agent Terrain', 'Investigations et validation terrain'),
        ('Agent Technique', 'Gestion technique et maintenance'),
        ('Agent Analyste', 'Analyse et validation des détections')
    ]

    for name, desc in authorities_data:
        authority, created = AuthorityModel.objects.get_or_create(
            name=name,
            defaults={'description': desc}
        )
        if created:
            print(f"   ✓ Autorité '{name}' créée")
        else:
            print(f"   - Autorité '{name}' existe déjà")
    
    # 2. Créer la région BONDOUKOU
    print("\n2. Création de la région...")
    region, created = RegionModel.objects.get_or_create(
        name='BONDOUKOU',
        defaults={
            'code': 'BDK',
            'area_km2': 12000,
            'center_lat': 8.0402,
            'center_lon': -2.8000
        }
    )
    if created:
        print("   ✓ Région BONDOUKOU créée")
    else:
        print("   - Région BONDOUKOU existe déjà")
    
    # 3. Créer le Responsable Régional
    print("\n3. Création du Responsable Régional...")
    responsable_email = '<EMAIL>'
    
    if UserModel.objects.filter(email=responsable_email).exists():
        print(f"   - Utilisateur {responsable_email} existe déjà")
        responsable = UserModel.objects.get(email=responsable_email)
    else:
        responsable = UserModel.objects.create(
            email=responsable_email,
            password=make_password('GoldSentinel2025!'),
            first_name='Kouadio',
            last_name='KOFFI',
            phone='+225 07 12 34 56 78',
            job_title='Responsable Régional Surveillance Orpaillage',
            institution='Ministère des Mines et de la Géologie - Direction Régionale Bondoukou',
            authorized_region='BONDOUKOU',
            is_active=True
        )
        print(f"   ✓ Utilisateur {responsable_email} créé")
    
    # 4. Assigner l'autorité
    print("\n4. Attribution des permissions...")
    authority = AuthorityModel.objects.get(name='Responsable Régional')
    user_auth, created = UserAuthorityModel.objects.get_or_create(
        user=responsable,
        authority=authority,
        defaults={'is_primary': True}
    )
    if created:
        print("   ✓ Autorité 'Responsable Régional' assignée")
    else:
        print("   - Autorité déjà assignée")
    
    # 5. Résumé
    print("\n" + "="*60)
    print("🎉 RESPONSABLE RÉGIONAL CRÉÉ AVEC SUCCÈS!")
    print("="*60)
    print(f"Email: {responsable.email}")
    print(f"Nom complet: {responsable.get_full_name()}")
    print(f"Téléphone: {responsable.phone}")
    print(f"Fonction: {responsable.job_title}")
    print(f"Institution: {responsable.institution}")
    print(f"Région autorisée: {responsable.authorized_region}")
    print(f"Statut: {'Actif' if responsable.is_active else 'Inactif'}")
    
    authorities = [ua.authority.name for ua in responsable.user_authorities.all()]
    print(f"Autorités: {', '.join(authorities)}")
    
    print("\n📋 INFORMATIONS DE CONNEXION:")
    print(f"   Email: {responsable.email}")
    print("   Mot de passe: GoldSentinel2025!")
    print("\n🌐 Vous pouvez maintenant vous connecter sur:")
    print("   Frontend: http://localhost:5175/")
    print("   Admin Django: http://localhost:8000/admin/")
    
    return responsable

if __name__ == "__main__":
    try:
        create_responsable_regional()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        sys.exit(1)
