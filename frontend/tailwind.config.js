export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Palette GoldSentinel
        gold: '#FFD700',
        'navy-blue': '#0B1E3F',
        'forest-green': '#1C6B48',
        'alert-red': '#E63946',
        white: '#FFFFFF',
        // Variations pour les états
        'gold-light': '#FFED4A',
        'gold-dark': '#F1C40F',
        'navy-light': '#1A365D',
        'navy-dark': '#0A1628',
        'forest-light': '#2D8659',
        'forest-dark': '#155A3C',
      },
      fontFamily: {
        // Polices pour les titres
        'title': ['Montserrat', 'Poppins', 'sans-serif'],
        // Polices pour le texte courant
        'body': ['Open Sans', 'Roboto', 'sans-serif'],
      },
      fontSize: {
        // Taille minimale lisible : 14px
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }], // 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      boxShadow: {
        'institutional': '0 4px 6px -1px rgba(11, 30, 63, 0.1), 0 2px 4px -1px rgba(11, 30, 63, 0.06)',
        'card': '0 10px 15px -3px rgba(11, 30, 63, 0.1), 0 4px 6px -2px rgba(11, 30, 63, 0.05)',
      },
      borderRadius: {
        'institutional': '0.375rem', // 6px - pas trop arrondi
      },
    },
  },
  plugins: [],
}
