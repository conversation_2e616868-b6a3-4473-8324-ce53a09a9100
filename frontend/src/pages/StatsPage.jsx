import { useState, useEffect } from 'react'
import { BarChart3, TrendingUp, TrendingDown, Activity } from 'lucide-react'
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts'

const StatsPage = () => {
  const [stats, setStats] = useState({
    overview: {},
    detectionsByMonth: [],
    detectionsByType: [],
    regionStats: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simuler le chargement des statistiques
    setTimeout(() => {
      setStats({
        overview: {
          totalDetections: 1247,
          validatedDetections: 892,
          activeAlerts: 23,
          completedInvestigations: 156,
          monthlyGrowth: 12.5,
          validationRate: 71.5
        },
        detectionsByMonth: [
          { month: 'Jan', detections: 89, validated: 67 },
          { month: 'Fév', detections: 124, validated: 89 },
          { month: 'Mar', detections: 156, validated: 112 },
          { month: 'Avr', detections: 178, validated: 134 },
          { month: 'Mai', detections: 203, validated: 145 },
          { month: 'Jun', detections: 234, validated: 167 }
        ],
        detectionsByType: [
          { name: 'Déforestation', value: 45, color: '#E63946' },
          { name: 'Exploitation minière', value: 30, color: '#FFD700' },
          { name: 'Construction', value: 15, color: '#1C6B48' },
          { name: 'Agriculture', value: 10, color: '#0B1E3F' }
        ],
        regionStats: [
          { region: 'Amazonas', detections: 456, alerts: 12 },
          { region: 'Pará', detections: 234, alerts: 8 },
          { region: 'Acre', detections: 189, alerts: 3 },
          { region: 'Rondônia', detections: 167, alerts: 5 }
        ]
      })
      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <h1 className="text-2xl font-title font-bold text-navy-blue">
          Statistiques et Analyses
        </h1>
        <p className="text-gray-600 font-body mt-1">
          Tableaux de bord et métriques de performance
        </p>
      </div>

      {/* Métriques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Détections Totales"
          value={stats.overview.totalDetections}
          change={stats.overview.monthlyGrowth}
          icon={Activity}
          color="blue"
        />
        <MetricCard
          title="Détections Validées"
          value={stats.overview.validatedDetections}
          change={stats.overview.validationRate}
          icon={BarChart3}
          color="green"
          isPercentage={true}
        />
        <MetricCard
          title="Alertes Actives"
          value={stats.overview.activeAlerts}
          change={-8.2}
          icon={TrendingDown}
          color="red"
        />
        <MetricCard
          title="Investigations"
          value={stats.overview.completedInvestigations}
          change={15.3}
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Évolution des détections */}
        <div className="bg-white rounded-institutional shadow-card p-6">
          <h3 className="text-lg font-title font-semibold text-navy-blue mb-4">
            Évolution des Détections
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={stats.detectionsByMonth}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="detections" fill="#FFD700" name="Détections" />
              <Bar dataKey="validated" fill="#1C6B48" name="Validées" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Répartition par type */}
        <div className="bg-white rounded-institutional shadow-card p-6">
          <h3 className="text-lg font-title font-semibold text-navy-blue mb-4">
            Répartition par Type
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={stats.detectionsByType}
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {stats.detectionsByType.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Statistiques par région */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <h3 className="text-lg font-title font-semibold text-navy-blue mb-4">
          Statistiques par Région
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="table-header">
              <tr>
                <th className="px-6 py-3 text-left">Région</th>
                <th className="px-6 py-3 text-left">Détections</th>
                <th className="px-6 py-3 text-left">Alertes Actives</th>
                <th className="px-6 py-3 text-left">Taux de Validation</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {stats.regionStats.map((region, index) => (
                <tr key={index} className="table-row">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 font-body">
                    {region.region}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-body">
                    {region.detections}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-body">
                    {region.alerts}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-body">
                    {Math.round((region.detections * 0.7))}%
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

// Composant MetricCard
const MetricCard = ({ title, value, change, icon: Icon, color, isPercentage = false }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    red: 'bg-red-50 text-red-600',
    purple: 'bg-purple-50 text-purple-600'
  }

  const isPositive = change > 0
  const changeColor = isPositive ? 'text-forest-green' : 'text-alert-red'
  const changeIcon = isPositive ? TrendingUp : TrendingDown

  return (
    <div className="bg-white rounded-institutional shadow-card p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-body text-gray-600">{title}</p>
          <p className="text-2xl font-title font-bold text-navy-blue">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          <div className="flex items-center mt-2">
            {React.createElement(changeIcon, { className: `h-4 w-4 ${changeColor} mr-1` })}
            <span className={`text-sm font-body ${changeColor}`}>
              {Math.abs(change).toFixed(1)}%{isPercentage ? ' taux' : ''}
            </span>
          </div>
        </div>
        <div className={`p-3 rounded-institutional ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </div>
  )
}

export default StatsPage
