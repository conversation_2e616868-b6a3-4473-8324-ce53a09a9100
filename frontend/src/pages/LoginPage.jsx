import { useState } from 'react'
import { Navigate } from 'react-router-dom'
import { Shield, Eye, EyeOff, AlertCircle } from 'lucide-react'
import { useAuth } from '../context/AuthContext'

const LoginPage = () => {
  const { login, isAuthenticated } = useAuth()
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  // Rediriger si déjà connecté
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    // Effacer l'erreur quand l'utilisateur tape
    if (error) setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const result = await login(formData)
    
    if (!result.success) {
      setError(result.error)
    }
    
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy-blue via-navy-light to-forest-green flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo et titre */}
        <div className="text-center">
          <div className="mx-auto h-20 w-20 bg-gold rounded-full flex items-center justify-center shadow-institutional">
            <Shield className="h-12 w-12 text-navy-blue" />
          </div>
          <h1 className="mt-6 text-4xl font-title font-bold text-white">
            GoldSentinel
          </h1>
          <p className="mt-2 text-lg text-gold font-body">
            Système de surveillance environnementale
          </p>
          <p className="mt-1 text-sm text-gray-300 font-body">
            Connectez-vous pour accéder au système
          </p>
        </div>

        {/* Formulaire de connexion */}
        <div className="card bg-white/95 backdrop-blur-sm">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Affichage des erreurs */}
            {error && (
              <div className="bg-alert-red/10 border border-alert-red/20 rounded-institutional p-4 flex items-center space-x-3">
                <AlertCircle className="h-5 w-5 text-alert-red flex-shrink-0" />
                <p className="text-sm text-alert-red font-body">{error}</p>
              </div>
            )}

            {/* Champ nom d'utilisateur */}
            <div>
              <label htmlFor="username" className="block text-sm font-semibold text-navy-blue font-title mb-2">
                Nom d'utilisateur
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                className="input-field"
                placeholder="Entrez votre nom d'utilisateur"
                value={formData.username}
                onChange={handleChange}
                disabled={loading}
              />
            </div>

            {/* Champ mot de passe */}
            <div>
              <label htmlFor="password" className="block text-sm font-semibold text-navy-blue font-title mb-2">
                Mot de passe
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  className="input-field pr-10"
                  placeholder="Entrez votre mot de passe"
                  value={formData.password}
                  onChange={handleChange}
                  disabled={loading}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
            </div>

            {/* Bouton de connexion */}
            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-navy-blue"></div>
                ) : (
                  'Se connecter'
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Informations supplémentaires */}
        <div className="text-center">
          <p className="text-xs text-gray-300 font-body">
            Système sécurisé - Accès autorisé uniquement
          </p>
          <p className="text-xs text-gray-400 font-body mt-1">
            Version 3.0 - © 2024 GoldSentinel
          </p>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
