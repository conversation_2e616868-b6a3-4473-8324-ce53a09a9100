import { useState, useEffect } from 'react'
import { Search, User, Calendar, FileText } from 'lucide-react'

const InvestigationsPage = () => {
  const [investigations, setInvestigations] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simuler le chargement des investigations
    setTimeout(() => {
      setInvestigations([
        {
          id: 1,
          title: 'Investigation déforestation A12',
          description: 'Enquête sur la déforestation massive dans le secteur A12',
          status: 'ongoing',
          priority: 'high',
          assigned_to: 'Agent <PERSON>',
          created_at: '2024-01-15T10:30:00Z',
          due_date: '2024-01-20T23:59:59Z',
          progress: 65
        },
        {
          id: 2,
          title: 'Vérification activité minière B7',
          description: 'Contrôle sur site de l\'activité minière suspecte',
          status: 'completed',
          priority: 'medium',
          assigned_to: 'Agent <PERSON>',
          created_at: '2024-01-10T14:20:00Z',
          completed_at: '2024-01-14T16:45:00Z',
          progress: 100
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getStatusBadge = (status) => {
    switch (status) {
      case 'ongoing':
        return <span className="badge-warning">En cours</span>
      case 'completed':
        return <span className="badge-success">Terminée</span>
      case 'pending':
        return <span className="badge-info">En attente</span>
      default:
        return <span className="badge-info">{status}</span>
    }
  }

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'high':
        return <span className="badge-danger">Haute</span>
      case 'medium':
        return <span className="badge-warning">Moyenne</span>
      case 'low':
        return <span className="badge-info">Faible</span>
      default:
        return <span className="badge-info">{priority}</span>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-title font-bold text-navy-blue">
              Investigations
            </h1>
            <p className="text-gray-600 font-body mt-1">
              Suivi et gestion des investigations terrain
            </p>
          </div>
          <button className="btn-primary">
            Nouvelle investigation
          </button>
        </div>
      </div>

      {/* Liste des investigations */}
      <div className="space-y-4">
        {investigations.map((investigation) => (
          <div key={investigation.id} className="bg-white rounded-institutional shadow-card p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="p-2 bg-forest-green/10 rounded-institutional">
                  <Search className="h-6 w-6 text-forest-green" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-title font-semibold text-navy-blue">
                    {investigation.title}
                  </h3>
                  <p className="text-gray-600 font-body mt-1">
                    {investigation.description}
                  </p>
                  
                  <div className="flex items-center space-x-6 mt-3">
                    <div className="flex items-center text-sm text-gray-500 font-body">
                      <User className="h-4 w-4 mr-1" />
                      {investigation.assigned_to}
                    </div>
                    <div className="flex items-center text-sm text-gray-500 font-body">
                      <Calendar className="h-4 w-4 mr-1" />
                      Créée le {new Date(investigation.created_at).toLocaleDateString('fr-FR')}
                    </div>
                    {investigation.due_date && (
                      <div className="flex items-center text-sm text-gray-500 font-body">
                        <Calendar className="h-4 w-4 mr-1" />
                        Échéance: {new Date(investigation.due_date).toLocaleDateString('fr-FR')}
                      </div>
                    )}
                  </div>

                  {/* Barre de progression */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm font-body mb-1">
                      <span className="text-gray-600">Progression</span>
                      <span className="text-navy-blue font-semibold">{investigation.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-forest-green h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${investigation.progress}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col items-end space-y-2">
                {getPriorityBadge(investigation.priority)}
                {getStatusBadge(investigation.status)}
              </div>
            </div>
            
            <div className="mt-4 flex space-x-3">
              <button className="btn-primary">
                Voir détails
              </button>
              <button className="btn-secondary">
                <FileText className="h-4 w-4 mr-2" />
                Rapport
              </button>
              {investigation.status === 'ongoing' && (
                <button className="btn-success">
                  Mettre à jour
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default InvestigationsPage
