import { useState, useEffect } from 'react'
import { Eye, MapPin, Calendar, Filter, Search, CheckCircle, XCircle } from 'lucide-react'

const DetectionsPage = () => {
  const [detections, setDetections] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    status: 'all',
    region: 'all',
    dateRange: 'week'
  })
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Simuler le chargement des détections
    // TODO: Remplacer par de vraies données de l'API
    setTimeout(() => {
      setDetections([
        {
          id: 1,
          timestamp: '2024-01-15T10:30:00Z',
          location: { latitude: -3.4653, longitude: -62.2159, name: 'Secteur A12' },
          confidence: 0.92,
          status: 'pending',
          type: 'deforestation',
          area: 2.5,
          region: 'Amazonas',
          image_url: '/api/images/detection_1.jpg'
        },
        {
          id: 2,
          timestamp: '2024-01-15T09:15:00Z',
          location: { latitude: -3.4123, longitude: -62.1876, name: 'Zone B7' },
          confidence: 0.87,
          status: 'validated',
          type: 'mining',
          area: 1.8,
          region: 'Amazonas',
          image_url: '/api/images/detection_2.jpg'
        },
        {
          id: 3,
          timestamp: '2024-01-14T16:45:00Z',
          location: { latitude: -3.5234, longitude: -62.3456, name: 'Secteur C3' },
          confidence: 0.78,
          status: 'rejected',
          type: 'deforestation',
          area: 0.9,
          region: 'Amazonas',
          image_url: '/api/images/detection_3.jpg'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <span className="badge-warning">En attente</span>
      case 'validated':
        return <span className="badge-success">Validée</span>
      case 'rejected':
        return <span className="badge-danger">Rejetée</span>
      default:
        return <span className="badge-info">{status}</span>
    }
  }

  const getTypeBadge = (type) => {
    const types = {
      'deforestation': 'Déforestation',
      'mining': 'Exploitation minière',
      'construction': 'Construction'
    }
    return types[type] || type
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleValidate = (id) => {
    setDetections(prev => 
      prev.map(detection => 
        detection.id === id 
          ? { ...detection, status: 'validated' }
          : detection
      )
    )
  }

  const handleReject = (id) => {
    setDetections(prev => 
      prev.map(detection => 
        detection.id === id 
          ? { ...detection, status: 'rejected' }
          : detection
      )
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-title font-bold text-navy-blue">
              Détections
            </h1>
            <p className="text-gray-600 font-body mt-1">
              Gestion et validation des détections automatiques
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 font-body">
              {detections.length} détections
            </span>
          </div>
        </div>
      </div>

      {/* Filtres et recherche */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-semibold text-navy-blue font-title mb-2">
              Recherche
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher..."
                className="input-field pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-semibold text-navy-blue font-title mb-2">
              Statut
            </label>
            <select
              className="input-field"
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
            >
              <option value="all">Tous</option>
              <option value="pending">En attente</option>
              <option value="validated">Validées</option>
              <option value="rejected">Rejetées</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-navy-blue font-title mb-2">
              Région
            </label>
            <select
              className="input-field"
              value={filters.region}
              onChange={(e) => setFilters({...filters, region: e.target.value})}
            >
              <option value="all">Toutes</option>
              <option value="Amazonas">Amazonas</option>
              <option value="Para">Pará</option>
              <option value="Acre">Acre</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-navy-blue font-title mb-2">
              Période
            </label>
            <select
              className="input-field"
              value={filters.dateRange}
              onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
            >
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
              <option value="all">Toutes</option>
            </select>
          </div>
        </div>
      </div>

      {/* Liste des détections */}
      <div className="bg-white rounded-institutional shadow-card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="table-header">
              <tr>
                <th className="px-6 py-3 text-left">Date/Heure</th>
                <th className="px-6 py-3 text-left">Localisation</th>
                <th className="px-6 py-3 text-left">Type</th>
                <th className="px-6 py-3 text-left">Confiance</th>
                <th className="px-6 py-3 text-left">Surface</th>
                <th className="px-6 py-3 text-left">Statut</th>
                <th className="px-6 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {detections.map((detection) => (
                <tr key={detection.id} className="table-row">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-sm text-gray-900 font-body">
                        {formatDate(detection.timestamp)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 font-body">
                          {detection.location.name}
                        </div>
                        <div className="text-sm text-gray-500 font-body">
                          {detection.region}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="text-sm text-gray-900 font-body">
                      {getTypeBadge(detection.type)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className="bg-gold h-2 rounded-full" 
                          style={{ width: `${detection.confidence * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-900 font-body">
                        {Math.round(detection.confidence * 100)}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-body">
                    {detection.area} ha
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(detection.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {detection.status === 'pending' && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleValidate(detection.id)}
                          className="text-forest-green hover:text-forest-dark"
                          title="Valider"
                        >
                          <CheckCircle className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleReject(detection.id)}
                          className="text-alert-red hover:text-red-700"
                          title="Rejeter"
                        >
                          <XCircle className="h-5 w-5" />
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default DetectionsPage
