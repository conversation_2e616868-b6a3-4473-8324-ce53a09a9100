import { useState, useEffect } from 'react'
import { Users, Plus, Edit, Trash2, Shield, Mail, Phone } from 'lucide-react'

const UsersPage = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simuler le chargement des utilisateurs
    setTimeout(() => {
      setUsers([
        {
          id: 1,
          username: 'admin',
          first_name: '<PERSON>',
          last_name: '<PERSON>',
          email: '<EMAIL>',
          role: 'admin',
          is_active: true,
          last_login: '2024-01-15T10:30:00Z',
          region: null
        },
        {
          id: 2,
          username: 'manager_amazonas',
          first_name: '<PERSON>',
          last_name: '<PERSON>',
          email: '<EMAIL>',
          role: 'regional_manager',
          is_active: true,
          last_login: '2024-01-15T09:15:00Z',
          region: 'Amazonas'
        },
        {
          id: 3,
          username: 'agent_silva',
          first_name: '<PERSON>',
          last_name: '<PERSON>',
          email: '<EMAIL>',
          role: 'field_agent',
          is_active: true,
          last_login: '2024-01-14T16:45:00Z',
          region: 'Amazonas'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getRoleBadge = (role) => {
    const roles = {
      'admin': { label: 'Administrateur', class: 'badge-danger' },
      'regional_manager': { label: 'Responsable Régional', class: 'badge-warning' },
      'field_agent': { label: 'Agent Terrain', class: 'badge-info' },
      'technical_agent': { label: 'Agent Technique', class: 'badge-success' },
      'analyst_agent': { label: 'Agent Analyste', class: 'badge-info' }
    }
    const roleInfo = roles[role] || { label: role, class: 'badge-info' }
    return <span className={roleInfo.class}>{roleInfo.label}</span>
  }

  const getStatusBadge = (isActive) => {
    return isActive 
      ? <span className="badge-success">Actif</span>
      : <span className="badge-danger">Inactif</span>
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-title font-bold text-navy-blue">
              Gestion des Utilisateurs
            </h1>
            <p className="text-gray-600 font-body mt-1">
              Administration des comptes et permissions
            </p>
          </div>
          <button className="btn-primary flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            Nouvel utilisateur
          </button>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-institutional shadow-card p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-institutional bg-blue-50 text-blue-600">
              <Users className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-body text-gray-600">Total</p>
              <p className="text-2xl font-title font-bold text-navy-blue">{users.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-institutional shadow-card p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-institutional bg-green-50 text-green-600">
              <Shield className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-body text-gray-600">Actifs</p>
              <p className="text-2xl font-title font-bold text-navy-blue">
                {users.filter(u => u.is_active).length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Table des utilisateurs */}
      <div className="bg-white rounded-institutional shadow-card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="table-header">
              <tr>
                <th className="px-6 py-3 text-left">Utilisateur</th>
                <th className="px-6 py-3 text-left">Contact</th>
                <th className="px-6 py-3 text-left">Rôle</th>
                <th className="px-6 py-3 text-left">Région</th>
                <th className="px-6 py-3 text-left">Statut</th>
                <th className="px-6 py-3 text-left">Dernière connexion</th>
                <th className="px-6 py-3 text-left">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id} className="table-row">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-navy-blue rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold">
                          {user.first_name[0]}{user.last_name[0]}
                        </span>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 font-body">
                          {user.first_name} {user.last_name}
                        </div>
                        <div className="text-sm text-gray-500 font-body">
                          @{user.username}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-500 font-body">
                      <Mail className="h-4 w-4 mr-1" />
                      {user.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getRoleBadge(user.role)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-body">
                    {user.region || 'Toutes'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(user.is_active)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-body">
                    {user.last_login 
                      ? new Date(user.last_login).toLocaleDateString('fr-FR')
                      : 'Jamais'
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button className="text-navy-blue hover:text-navy-light">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-alert-red hover:text-red-700">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

export default UsersPage
