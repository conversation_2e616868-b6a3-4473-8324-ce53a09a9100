import { useState, useEffect } from 'react'
import { AlertTriangle, Clock, CheckCircle, MapPin } from 'lucide-react'

const AlertsPage = () => {
  const [alerts, setAlerts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simuler le chargement des alertes
    setTimeout(() => {
      setAlerts([
        {
          id: 1,
          title: 'Déforestation massive détectée',
          description: 'Zone de 15 hectares déboisée en 24h',
          severity: 'high',
          status: 'active',
          location: 'Secteur A12 - Amazonas',
          created_at: '2024-01-15T10:30:00Z',
          detection_id: 1
        },
        {
          id: 2,
          title: 'Activité minière suspecte',
          description: 'Équipements lourds détectés dans zone protégée',
          severity: 'medium',
          status: 'investigating',
          location: 'Zone B7 - Amazonas',
          created_at: '2024-01-15T08:15:00Z',
          detection_id: 2
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const getSeverityBadge = (severity) => {
    switch (severity) {
      case 'high':
        return <span className="badge-danger">Critique</span>
      case 'medium':
        return <span className="badge-warning">Modérée</span>
      case 'low':
        return <span className="badge-info">Faible</span>
      default:
        return <span className="badge-info">{severity}</span>
    }
  }

  const getStatusBadge = (status) => {
    switch (status) {
      case 'active':
        return <span className="badge-danger">Active</span>
      case 'investigating':
        return <span className="badge-warning">En investigation</span>
      case 'resolved':
        return <span className="badge-success">Résolue</span>
      default:
        return <span className="badge-info">{status}</span>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <h1 className="text-2xl font-title font-bold text-navy-blue">
          Alertes
        </h1>
        <p className="text-gray-600 font-body mt-1">
          Gestion des alertes et notifications du système
        </p>
      </div>

      {/* Liste des alertes */}
      <div className="space-y-4">
        {alerts.map((alert) => (
          <div key={alert.id} className="bg-white rounded-institutional shadow-card p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="p-2 bg-alert-red/10 rounded-institutional">
                  <AlertTriangle className="h-6 w-6 text-alert-red" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-title font-semibold text-navy-blue">
                    {alert.title}
                  </h3>
                  <p className="text-gray-600 font-body mt-1">
                    {alert.description}
                  </p>
                  <div className="flex items-center space-x-4 mt-3">
                    <div className="flex items-center text-sm text-gray-500 font-body">
                      <MapPin className="h-4 w-4 mr-1" />
                      {alert.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-500 font-body">
                      <Clock className="h-4 w-4 mr-1" />
                      {new Date(alert.created_at).toLocaleString('fr-FR')}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                {getSeverityBadge(alert.severity)}
                {getStatusBadge(alert.status)}
              </div>
            </div>
            <div className="mt-4 flex space-x-3">
              <button className="btn-primary">
                Voir détails
              </button>
              <button className="btn-secondary">
                Créer investigation
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default AlertsPage
