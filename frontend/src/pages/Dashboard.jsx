import { useState, useEffect } from 'react'
import { 
  Eye, 
  AlertTriangle, 
  Search, 
  Users, 
  TrendingUp, 
  MapPin,
  Clock,
  CheckCircle
} from 'lucide-react'
import { useAuth } from '../context/AuthContext'

const Dashboard = () => {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    detections: { total: 0, pending: 0, validated: 0 },
    alerts: { total: 0, active: 0, resolved: 0 },
    investigations: { total: 0, ongoing: 0, completed: 0 },
    users: { total: 0, active: 0 }
  })

  useEffect(() => {
    // Simuler le chargement des statistiques
    // TODO: Remplacer par de vraies données de l'API
    setStats({
      detections: { total: 156, pending: 23, validated: 133 },
      alerts: { total: 45, active: 12, resolved: 33 },
      investigations: { total: 78, ongoing: 15, completed: 63 },
      users: { total: 24, active: 18 }
    })
  }, [])

  const getWelcomeMessage = () => {
    const hour = new Date().getHours()
    let greeting = 'Bonjour'
    if (hour >= 18) greeting = 'Bonsoir'
    else if (hour >= 12) greeting = 'Bon après-midi'
    
    return `${greeting}, ${user?.first_name || 'Utilisateur'}`
  }

  const getRoleSpecificWidgets = () => {
    const role = user?.role
    
    switch (role) {
      case 'admin':
        return (
          <>
            <StatCard
              title="Détections"
              value={stats.detections.total}
              subtitle={`${stats.detections.pending} en attente`}
              icon={Eye}
              color="blue"
            />
            <StatCard
              title="Alertes"
              value={stats.alerts.total}
              subtitle={`${stats.alerts.active} actives`}
              icon={AlertTriangle}
              color="red"
            />
            <StatCard
              title="Investigations"
              value={stats.investigations.total}
              subtitle={`${stats.investigations.ongoing} en cours`}
              icon={Search}
              color="green"
            />
            <StatCard
              title="Utilisateurs"
              value={stats.users.total}
              subtitle={`${stats.users.active} actifs`}
              icon={Users}
              color="purple"
            />
          </>
        )
      
      case 'regional_manager':
        return (
          <>
            <StatCard
              title="Détections Région"
              value={stats.detections.total}
              subtitle={`${stats.detections.pending} à valider`}
              icon={Eye}
              color="blue"
            />
            <StatCard
              title="Alertes Région"
              value={stats.alerts.total}
              subtitle={`${stats.alerts.active} actives`}
              icon={AlertTriangle}
              color="red"
            />
            <StatCard
              title="Investigations"
              value={stats.investigations.total}
              subtitle={`${stats.investigations.ongoing} en cours`}
              icon={Search}
              color="green"
            />
            <StatCard
              title="Équipe"
              value={stats.users.active}
              subtitle="agents actifs"
              icon={Users}
              color="purple"
            />
          </>
        )
      
      case 'analyst_agent':
        return (
          <>
            <StatCard
              title="À Analyser"
              value={stats.detections.pending}
              subtitle="détections en attente"
              icon={Eye}
              color="blue"
            />
            <StatCard
              title="Validées"
              value={stats.detections.validated}
              subtitle="cette semaine"
              icon={CheckCircle}
              color="green"
            />
            <StatCard
              title="Alertes"
              value={stats.alerts.active}
              subtitle="nécessitent attention"
              icon={AlertTriangle}
              color="red"
            />
            <StatCard
              title="Rapports"
              value={12}
              subtitle="générés ce mois"
              icon={TrendingUp}
              color="purple"
            />
          </>
        )
      
      case 'field_agent':
        return (
          <>
            <StatCard
              title="Missions"
              value={8}
              subtitle="assignées"
              icon={MapPin}
              color="blue"
            />
            <StatCard
              title="Investigations"
              value={stats.investigations.ongoing}
              subtitle="en cours"
              icon={Search}
              color="green"
            />
            <StatCard
              title="Alertes"
              value={stats.alerts.active}
              subtitle="dans votre zone"
              icon={AlertTriangle}
              color="red"
            />
            <StatCard
              title="Rapports"
              value={5}
              subtitle="à soumettre"
              icon={Clock}
              color="purple"
            />
          </>
        )
      
      case 'technical_agent':
        return (
          <>
            <StatCard
              title="Images"
              value={1247}
              subtitle="traitées aujourd'hui"
              icon={Eye}
              color="blue"
            />
            <StatCard
              title="Système"
              value="99.8%"
              subtitle="disponibilité"
              icon={CheckCircle}
              color="green"
            />
            <StatCard
              title="Alertes Tech"
              value={3}
              subtitle="nécessitent intervention"
              icon={AlertTriangle}
              color="red"
            />
            <StatCard
              title="Maintenance"
              value={2}
              subtitle="tâches planifiées"
              icon={Clock}
              color="purple"
            />
          </>
        )
      
      default:
        return (
          <StatCard
            title="Bienvenue"
            value="GoldSentinel"
            subtitle="Système de surveillance"
            icon={Eye}
            color="blue"
          />
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <h1 className="text-2xl font-title font-bold text-navy-blue">
          {getWelcomeMessage()}
        </h1>
        <p className="text-gray-600 font-body mt-1">
          Voici un aperçu de votre tableau de bord GoldSentinel
        </p>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {getRoleSpecificWidgets()}
      </div>

      {/* Activité récente */}
      <div className="bg-white rounded-institutional shadow-card p-6">
        <h2 className="text-lg font-title font-semibold text-navy-blue mb-4">
          Activité Récente
        </h2>
        <div className="space-y-3">
          <ActivityItem
            icon={Eye}
            title="Nouvelle détection validée"
            description="Zone forestière - Secteur A12"
            time="Il y a 15 minutes"
            type="success"
          />
          <ActivityItem
            icon={AlertTriangle}
            title="Alerte générée"
            description="Activité suspecte détectée"
            time="Il y a 1 heure"
            type="warning"
          />
          <ActivityItem
            icon={Search}
            title="Investigation terminée"
            description="Rapport soumis pour validation"
            time="Il y a 2 heures"
            type="info"
          />
        </div>
      </div>
    </div>
  )
}

// Composant StatCard
const StatCard = ({ title, value, subtitle, icon: Icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    red: 'bg-red-50 text-red-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600'
  }

  return (
    <div className="bg-white rounded-institutional shadow-card p-6">
      <div className="flex items-center">
        <div className={`p-3 rounded-institutional ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-body text-gray-600">{title}</p>
          <p className="text-2xl font-title font-bold text-navy-blue">{value}</p>
          <p className="text-sm font-body text-gray-500">{subtitle}</p>
        </div>
      </div>
    </div>
  )
}

// Composant ActivityItem
const ActivityItem = ({ icon: Icon, title, description, time, type }) => {
  const typeClasses = {
    success: 'text-forest-green',
    warning: 'text-gold',
    info: 'text-navy-blue'
  }

  return (
    <div className="flex items-start space-x-3 p-3 rounded-institutional hover:bg-gray-50">
      <Icon className={`h-5 w-5 mt-0.5 ${typeClasses[type]}`} />
      <div className="flex-1">
        <p className="text-sm font-semibold text-navy-blue font-body">{title}</p>
        <p className="text-sm text-gray-600 font-body">{description}</p>
        <p className="text-xs text-gray-500 font-body mt-1">{time}</p>
      </div>
    </div>
  )
}

export default Dashboard
