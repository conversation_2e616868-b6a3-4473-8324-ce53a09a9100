import axios from 'axios'

// Configuration de base d'axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Intercepteur pour ajouter le token JWT aux requêtes
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Intercepteur pour gérer les erreurs d'authentification
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export const authService = {
  // Connexion
  login: async (credentials) => {
    const response = await api.post('/auth/login/', credentials)
    return response.data
  },

  // Déconnexion
  logout: async () => {
    try {
      await api.post('/auth/logout/')
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error)
    }
  },

  // Obtenir l'utilisateur actuel
  getCurrentUser: async () => {
    const response = await api.get('/auth/user/')
    return response.data
  },

  // Rafraîchir le token
  refreshToken: async () => {
    const response = await api.post('/auth/refresh/')
    return response.data
  },

  // Changer le mot de passe
  changePassword: async (passwordData) => {
    const response = await api.post('/auth/change-password/', passwordData)
    return response.data
  }
}

export default api
