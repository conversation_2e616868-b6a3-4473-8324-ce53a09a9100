import { Link, useLocation } from 'react-router-dom'
import { 
  Home, 
  Eye, 
  AlertTriangle, 
  Search, 
  Users, 
  BarChart3, 
  Settings,
  Map,
  FileText,
  Camera,
  X
} from 'lucide-react'

const Sidebar = ({ isOpen, onClose, userRole }) => {
  const location = useLocation()

  // Configuration des menus selon les rôles
  const getMenuItems = (role) => {
    const baseItems = [
      { name: 'Dashboard', href: '/dashboard', icon: Home, roles: ['admin', 'regional_manager', 'field_agent', 'technical_agent', 'analyst_agent'] }
    ]

    const menuItems = [
      ...baseItems,
      { name: 'Détections', href: '/detections', icon: Eye, roles: ['admin', 'regional_manager', 'analyst_agent', 'technical_agent'] },
      { name: 'Alert<PERSON>', href: '/alerts', icon: AlertTriangle, roles: ['admin', 'regional_manager', 'field_agent', 'analyst_agent'] },
      { name: 'Investigations', href: '/investigations', icon: Search, roles: ['admin', 'regional_manager', 'field_agent', 'analyst_agent'] },
      { name: '<PERSON><PERSON>', href: '/maps', icon: Map, roles: ['admin', 'regional_manager', 'field_agent', 'analyst_agent'] },
      { name: 'Images', href: '/images', icon: Camera, roles: ['admin', 'technical_agent', 'analyst_agent'] },
      { name: 'Rapports', href: '/reports', icon: FileText, roles: ['admin', 'regional_manager', 'analyst_agent'] },
      { name: 'Utilisateurs', href: '/users', icon: Users, roles: ['admin', 'regional_manager'] },
      { name: 'Statistiques', href: '/stats', icon: BarChart3, roles: ['admin', 'regional_manager', 'analyst_agent'] },
      { name: 'Paramètres', href: '/settings', icon: Settings, roles: ['admin', 'technical_agent'] }
    ]

    return menuItems.filter(item => item.roles.includes(role))
  }

  const menuItems = getMenuItems(userRole)

  const isActive = (href) => {
    return location.pathname === href
  }

  return (
    <>
      {/* Overlay pour mobile */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-navy-blue transform transition-transform duration-300 ease-in-out
        lg:translate-x-0 lg:static lg:inset-0
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-navy-light">
          <h2 className="text-lg font-title font-semibold text-white">
            Navigation
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-institutional text-gray-300 hover:text-white hover:bg-navy-light lg:hidden"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-body font-medium rounded-institutional transition-colors duration-200
                    ${active 
                      ? 'bg-gold text-navy-blue' 
                      : 'text-gray-300 hover:bg-navy-light hover:text-white'
                    }
                  `}
                >
                  <Icon className={`
                    mr-3 h-5 w-5 flex-shrink-0
                    ${active ? 'text-navy-blue' : 'text-gray-400 group-hover:text-white'}
                  `} />
                  {item.name}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Informations utilisateur en bas */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-navy-light">
          <div className="text-center">
            <p className="text-xs text-gray-400 font-body">
              Rôle: {getRoleDisplayName(userRole)}
            </p>
            <p className="text-xs text-gray-500 font-body mt-1">
              GoldSentinel v3.0
            </p>
          </div>
        </div>
      </div>
    </>
  )
}

const getRoleDisplayName = (role) => {
  const roleNames = {
    'admin': 'Administrateur',
    'regional_manager': 'Responsable Régional',
    'field_agent': 'Agent Terrain',
    'technical_agent': 'Agent Technique',
    'analyst_agent': 'Agent Analyste'
  }
  return roleNames[role] || role
}

export default Sidebar
