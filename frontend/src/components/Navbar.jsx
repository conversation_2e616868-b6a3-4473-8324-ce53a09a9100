import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, User, LogOut, Setting<PERSON>, Shield } from 'lucide-react'
import { useAuth } from '../context/AuthContext'

const Navbar = ({ onMenuClick, user }) => {
  const { logout } = useAuth()
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [notifications] = useState([
    { id: 1, message: 'Nouvelle détection en attente', type: 'warning' },
    { id: 2, message: 'Investigation terminée', type: 'success' },
  ])

  const getRoleDisplayName = (role) => {
    const roleNames = {
      'admin': 'Administrateur',
      'regional_manager': 'Responsable Régional',
      'field_agent': 'Agent Terrain',
      'technical_agent': 'Agent Technique',
      'analyst_agent': 'Agent Analyste'
    }
    return roleNames[role] || role
  }

  const handleLogout = () => {
    logout()
  }

  return (
    <nav className="bg-white shadow-institutional border-b border-gray-200">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Left side */}
          <div className="flex items-center">
            {/* Menu button for mobile */}
            <button
              onClick={onMenuClick}
              className="p-2 rounded-institutional text-gray-600 hover:text-navy-blue hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gold lg:hidden"
            >
              <Menu className="h-6 w-6" />
            </button>

            {/* Logo and title */}
            <div className="flex items-center ml-4 lg:ml-0">
              <div className="h-8 w-8 bg-gold rounded-full flex items-center justify-center">
                <Shield className="h-5 w-5 text-navy-blue" />
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-title font-bold text-navy-blue">
                  GoldSentinel
                </h1>
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <div className="relative">
              <button className="p-2 rounded-institutional text-gray-600 hover:text-navy-blue hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gold">
                <Bell className="h-6 w-6" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-alert-red text-white text-xs rounded-full flex items-center justify-center">
                    {notifications.length}
                  </span>
                )}
              </button>
            </div>

            {/* User menu */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 p-2 rounded-institutional text-gray-600 hover:text-navy-blue hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gold"
              >
                <div className="h-8 w-8 bg-navy-blue rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div className="hidden md:block text-left">
                  <p className="text-sm font-semibold text-navy-blue font-title">
                    {user?.first_name} {user?.last_name}
                  </p>
                  <p className="text-xs text-gray-500 font-body">
                    {getRoleDisplayName(user?.role)}
                  </p>
                </div>
              </button>

              {/* User dropdown menu */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-institutional shadow-card border border-gray-200 py-1 z-50">
                  <div className="px-4 py-2 border-b border-gray-200">
                    <p className="text-sm font-semibold text-navy-blue font-title">
                      {user?.first_name} {user?.last_name}
                    </p>
                    <p className="text-xs text-gray-500 font-body">
                      {user?.email}
                    </p>
                  </div>
                  
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 font-body">
                    <User className="h-4 w-4" />
                    <span>Profil</span>
                  </button>
                  
                  <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 font-body">
                    <Settings className="h-4 w-4" />
                    <span>Paramètres</span>
                  </button>
                  
                  <div className="border-t border-gray-200 mt-1">
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-alert-red hover:bg-gray-100 flex items-center space-x-2 font-body"
                    >
                      <LogOut className="h-4 w-4" />
                      <span>Déconnexion</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
