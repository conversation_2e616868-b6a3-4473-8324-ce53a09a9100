
from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from account.models.user_model import UserModel
from account.models.authority_model import AuthorityModel
from account.models.user_authority_model import UserAuthorityModel
from region.models.region_model import RegionModel

class Command(BaseCommand):
    help = 'Crée les utilisateurs par défaut : Agent Terrain + Responsable Régional'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force la recréation des utilisateurs (supprime les existants)',
        )
        parser.add_argument(
            '--demo',
            action='store_true',
            help='Crée des données de démonstration supplémentaires',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.HTTP_INFO('Création utilisateurs par défaut Gold Sentinel')
        )
        self.stdout.write('=' * 60)
        
        # Force suppression si demandé
        if options['force']:
            self._delete_existing_users()
        
        # Création région et autorités
        self._ensure_region_and_authorities()
        
        # Création utilisateurs
        responsable = self._create_responsable_regional()
        agent = self._create_agent_terrain()
        
        # Données demo optionnelles
        if options['demo']:
            self._create_demo_data(responsable, agent)
        
        self._display_summary(responsable, agent)

    def _delete_existing_users(self):
        """Supprime utilisateurs existants"""
        default_emails = [
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        deleted_count = UserModel.objects.filter(email__in=default_emails).delete()[0]
        if deleted_count > 0:
            self.stdout.write(f"{deleted_count} utilisateurs existants supprimés")

    def _ensure_region_and_authorities(self):
        """S'assure que région et autorités existent"""
        # Région Bondoukou
        region, created = RegionModel.objects.get_or_create(
            name='BONDOUKOU',
            defaults={
                'code': 'BDK',
                'area_km2': 12000,
                'center_lat': 8.0402,
                'center_lon': -2.8000
            }
        )
        if created:
            self.stdout.write("Région BONDOUKOU créée")
        
        # Autorités
        authorities = [
            ('Responsable Régional', 'Supervision complète surveillance orpaillage'),
            ('Agent Terrain', 'Investigations et validation terrain')
        ]
        
        for name, desc in authorities:
            authority, created = AuthorityModel.objects.get_or_create(
                name=name,
                defaults={'description': desc}
            )
            if created:
                self.stdout.write(f"Autorité '{name}' créée")

    def _create_responsable_regional(self):
        """Crée le Responsable Régional"""
        user, created = UserModel.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': make_password('GoldSentinel2025!'),
                'first_name': 'Kouadio',
                'last_name': 'KOFFI',
                'phone': '+225 07 12 34 56 78',
                'job_title': 'Responsable Régional Surveillance Orpaillage',
                'institution': 'Ministère des Mines et de la Géologie - Direction Régionale Bondoukou',
                'authorized_region': 'BONDOUKOU',
                'is_active': True
            }
        )
        
        if created:
            # Assigner autorité
            authority = AuthorityModel.objects.get(name='Responsable Régional')
            UserAuthorityModel.objects.get_or_create(
                user=user,
                authority=authority,
                defaults={'is_primary': True}
            )
            
            self.stdout.write(
                self.style.SUCCESS('Responsable Régional créé: <EMAIL>')
            )
        else:
            self.stdout.write('ℹResponsable Régional existe déjà')
        
        return user

    def _create_agent_terrain(self):
        """Crée l'Agent Terrain"""
        user, created = UserModel.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'password': make_password('Terrain2025!'),
                'first_name': 'Aminata',
                'last_name': 'TRAORE',
                'phone': '+225 05 98 76 54 32',
                'job_title': 'Agent Terrain Spécialisé Détection Orpaillage',
                'institution': 'Ministère des Mines et de la Géologie - Équipe Terrain Bondoukou',
                'authorized_region': 'BONDOUKOU',
                'is_active': True
            }
        )
        
        if created:
            # Assigner autorité
            authority = AuthorityModel.objects.get(name='Agent Terrain')
            UserAuthorityModel.objects.get_or_create(
                user=user,
                authority=authority,
                defaults={'is_primary': True}
            )
            
            self.stdout.write(
                self.style.SUCCESS('Agent Terrain créé: <EMAIL>')
            )
        else:
            self.stdout.write('ℹAgent Terrain existe déjà')
        
        return user

    def _create_demo_data(self, responsable, agent):
        """Crée données de démonstration"""
        from detection.models.investigation_model import InvestigationModel
        from detection.models.detection_model import DetectionModel
        from image.models.image_model import ImageModel
        from region.models.region_model import RegionModel
        from django.utils import timezone
        from datetime import datetime, timedelta
        
        try:
            # Créer image exemple si pas existe
            bondoukou_region = RegionModel.objects.get(name='BONDOUKOU')
            
            demo_image, created = ImageModel.objects.get_or_create(
                name='Demo_Bondoukou_20250530',
                defaults={
                    'region': bondoukou_region,
                    'capture_date': (datetime.now() - timedelta(days=2)).date(),
                    'satellite_source': 'SENTINEL2',
                    'cloud_coverage': 15.0,
                    'resolution': 10,
                    'gee_asset_id': 'DEMO_ASSET_2025',
                    'center_lat': 8.0402,
                    'center_lon': -2.8000,
                    'processing_status': 'COMPLETED',
                    'processed_at': timezone.now(),
                    'requested_by': responsable
                }
            )
            
            if created:
                # Créer détection exemple
                demo_detection = DetectionModel.objects.create(
                    image=demo_image,
                    region=bondoukou_region,
                    latitude=8.0425,
                    longitude=-2.7985,
                    detection_type='MINING_SITE',
                    confidence_score=0.82,
                    area_hectares=12.5,
                    ndvi_anomaly_score=0.75,
                    ndwi_anomaly_score=0.68,
                    ndti_anomaly_score=0.71,
                    validation_status='DETECTED'
                )
                
                # Créer investigation assignée à l'agent
                demo_investigation = InvestigationModel.objects.create(
                    detection=demo_detection,
                    target_coordinates="8.0425, -2.7985",
                    access_instructions="Zone accessible par piste depuis village de Laoudi-Ba. Prendre équipement de mesure turbidité eau.",
                    assigned_to=agent,
                    status='ASSIGNED'
                )
                
                self.stdout.write("Données de démonstration créées")
                self.stdout.write(f"  Détection: Lat {demo_detection.latitude}, Lon {demo_detection.longitude}")
                self.stdout.write(f"  Investigation assignée à: {agent.get_full_name()}")
        
        except Exception as e:
            self.stdout.write(f" Erreur création données demo: {e}")

    def _display_summary(self, responsable, agent):
        """Affiche résumé de création"""
        self.stdout.write('\n')
        self.stdout.write(
            self.style.HTTP_INFO('RÉSUMÉ UTILISATEURS PAR DÉFAUT:')
        )
        self.stdout.write('=' * 60)
        
        self.stdout.write('SUPÉRIEUR HIÉRARCHIQUE:')
        self.stdout.write(f'   Email: <EMAIL>')
        self.stdout.write(f'   Mot de passe: GoldSentinel2025!')
        self.stdout.write(f'   Nom: {responsable.get_full_name()}')
        self.stdout.write(f'   Poste: {responsable.job_title}')
        self.stdout.write(f'   Téléphone: {responsable.phone}')
        
        self.stdout.write('\n AGENT TERRAIN:')
        self.stdout.write(f'   Email: <EMAIL>')
        self.stdout.write(f'   Mot de passe: Terrain2025!')
        self.stdout.write(f'   Nom: {agent.get_full_name()}')
        self.stdout.write(f'   Poste: {agent.job_title}')
        self.stdout.write(f'   Téléphone: {agent.phone}')
        
        self.stdout.write('=' * 60)
        self.stdout.write('Hiérarchie: Responsable Régional → Agent Terrain')
        self.stdout.write('Zone: BONDOUKOU')
        self.stdout.write('Prêt pour démonstration!')
        
        self.stdout.write('\n TESTS RAPIDES:')
        self.stdout.write('curl -X POST http://localhost:8000/api/v1/auth/login/ \\')
        self.stdout.write('  -H "Content-Type: application/json" \\')
        self.stdout.write('  -d \'{"email": "<EMAIL>", "password": "GoldSentinel2025!"}\'')
