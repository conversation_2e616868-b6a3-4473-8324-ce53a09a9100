from typing import List, Dict, Optional
from django.utils import timezone

from config.financial_settings import FinancialSettings
from image.models.image_model import ImageModel
from detection.models.detection_model import DetectionModel
from alert.models.alert_model import AlertModel
from alert.models.financial_risk_model import FinancialRiskModel

from gee.services.earth_engine_service import EarthEngineService
from report.services.event_log_service import EventLogService


class MiningDetectionService:
    """Service de détection d'activités d'orpaillage"""

    def __init__(self):
        self.gee_service = EarthEngineService()

    def analyze_for_mining_activity(self, image_record: 'ImageModel') -> List[DetectionModel]:
        """
        Analyse une image pour détecter activités d'orpaillage

        Args:
            image_record: Enregistrement image à analyser

        Returns:
            Liste des détections trouvées
        """
        detections = []

        try:
            # Récupération image de référence (plus ancienne disponible)
            reference_image = (ImageModel.objects
                               .filter(region=image_record.region,
                                       processing_status='COMPLETED')
                               .exclude(id=image_record.id)
                               .order_by('capture_date')
                               .first())

            if not reference_image:
                print("Pas d'image de référence trouvée")
                return detections

            # Comparaison indices spectraux
            current_indices = {
                'ndvi_data': image_record.ndvi_data,
                'ndwi_data': image_record.ndwi_data,
                'ndti_data': image_record.ndti_data,
            }

            reference_indices = {
                'ndvi_data': reference_image.ndvi_data,
                'ndwi_data': reference_image.ndwi_data,
                'ndti_data': reference_image.ndti_data,
            }

            # Détection anomalies
            anomaly_scores = self.gee_service.detect_anomalies(current_indices, reference_indices)

            # Seuils de détection (à calibrer selon vos données)
            DETECTION_THRESHOLDS = {
                'ndvi_threshold': 0.3,  # Chute significative végétation
                'ndwi_threshold': 0.2,  # Changement eau/turbidité
                'ndti_threshold': 0.4,  # Perturbation sol
            }

            # Vérification seuils dépassés
            if (anomaly_scores.get('ndvi_anomaly_score', 0) > DETECTION_THRESHOLDS['ndvi_threshold'] or
                    anomaly_scores.get('ndwi_anomaly_score', 0) > DETECTION_THRESHOLDS['ndwi_threshold'] or
                    anomaly_scores.get('ndti_anomaly_score', 0) > DETECTION_THRESHOLDS['ndti_threshold']):
                # Création détection
                detection = DetectionModel.objects.create(
                    image=image_record,
                    region=image_record.region,
                    latitude=image_record.center_lat,
                    longitude=image_record.center_lon,
                    detection_type='MINING_SITE',  # Type principal
                    confidence_score=0,  # Calculé ci-dessous
                    area_hectares=self._estimate_affected_area(anomaly_scores),
                    ndvi_anomaly_score=anomaly_scores.get('ndvi_anomaly_score'),
                    ndwi_anomaly_score=anomaly_scores.get('ndwi_anomaly_score'),
                    ndti_anomaly_score=anomaly_scores.get('ndti_anomaly_score'),
                    validation_status='DETECTED'
                )

                # Calcul score confiance
                detection.confidence_score = detection.calculate_confidence_score()
                detection.save()

                detections.append(detection)

                # Génération alerte et risque financier
                self._generate_alert_and_risk(detection)

                # LOG ÉVÉNEMENT
                EventLogService.log_detection_created(detection)

                detections.append(detection)
                self._generate_alert_and_risk(detection)

            return detections

        except Exception as e:
            EventLogService.log_event(
                'SYSTEM_ERROR',
                f"Erreur analyse activité minière: {str(e)}",
                metadata={'image_id': image_record.id}
            )
            return detections

    def _estimate_affected_area(self, anomaly_scores: Dict) -> float:
        """Estime la surface affectée basée sur les scores d'anomalie"""
        # Logique simplifiée - à améliorer avec données réelles
        max_score = max(anomaly_scores.values())
        # Surface proportionnelle au score (1-50 hectares)
        return min(max_score * 50, 50)

    # Dans detection/services/mining_detection_service.py
    def _generate_alert_and_risk(self, detection: DetectionModel):
        """Génère alerte, risque financier ET investigation automatiquement"""
        try:
            # 1. Détermination type d'alerte selon score
            if detection.confidence_score >= 0.8:
                alert_type = 'CLANDESTINE_SITE'
                criticality = 'CRITICAL'
            elif detection.confidence_score >= 0.6:
                alert_type = 'SUSPICIOUS_ACTIVITY'
                criticality = 'HIGH'
            else:
                alert_type = 'SUSPICIOUS_ACTIVITY'
                criticality = 'MEDIUM'

            # 2. Création alerte
            alert = AlertModel.objects.create(
                name=f"Détection orpaillage - {detection.detection_date.strftime('%Y-%m-%d')}",
                detection=detection,
                region=detection.region,
                level=criticality,
                alert_type=alert_type,
                message=f"Activité d'orpaillage détectée avec un score de confiance de {detection.confidence_score:.2f}. "
                        f"Surface estimée: {detection.area_hectares:.1f} hectares.",
                alert_status='ACTIVE'
            )

            # LOG ALERTE
            EventLogService.log_event(
                'ALERT_GENERATED',
                f"Alerte {criticality} générée pour détection {detection.id}",
                detection=detection,
                alert=alert
            )

            # 3. Création risque financier
            financial_risk = FinancialRiskModel.objects.create(
                detection=detection,
                area_hectares=detection.area_hectares,
                cost_per_hectare=FinancialSettings.DEFAULT_COST_PER_HECTARE,
                sensitive_zone_distance_km=2.0,
                occurrence_count=1
            )

            # LOG RISQUE
            EventLogService.log_event(
                'FINANCIAL_RISK_CALCULATED',
                f"Risque financier calculé: {financial_risk.estimated_loss:,.0f} FCFA",
                detection=detection,
                metadata={'risk_level': financial_risk.risk_level, 'amount': financial_risk.estimated_loss}
            )

            # Calculs automatiques
            financial_risk.calculate_estimated_loss()
            financial_risk.risk_level = financial_risk.determine_risk_level()
            financial_risk.save()

            # 4. NOUVEAU : Création automatique investigation
            from detection.models.investigation_model import InvestigationModel

            investigation = InvestigationModel.objects.create(
                detection=detection,
                target_coordinates=f"{detection.latitude:.4f}, {detection.longitude:.4f}",
                access_instructions=f"Zone Bondoukou - Coordonnées GPS: {detection.latitude:.4f}, {detection.longitude:.4f}. "
                                    f"Surface estimée: {detection.area_hectares:.1f} hectares. "
                                    f"Confidence IA: {detection.confidence_score:.2f}",
                status='PENDING'
            )

            print(f"Alerte, risque financier et investigation créés pour détection {detection.id}")
            # LOG INVESTIGATION
            EventLogService.log_event(
                'INVESTIGATION_CREATED',
                f"Investigation créée automatiquement pour détection {detection.id}",
                detection=detection,
                metadata={'investigation_id': investigation.id}
            )

        except Exception as e:
            EventLogService.log_event(
                'SYSTEM_ERROR',
                f"Erreur génération alerte/risque/investigation: {str(e)}",
                detection=detection
            )